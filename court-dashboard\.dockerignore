# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Production
build

# Misc
.DS_Store
*.tgz
*.tar.gz

# Debug
*.log

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# IDE
.vscode
.idea

# Git
.git
.gitignore

# Testing
coverage
.nyc_output

# Database
data/
*.db
*.sqlite

# Documentation
README.md
docs/

# Docker
Dockerfile
docker-compose.yml
.dockerignore
