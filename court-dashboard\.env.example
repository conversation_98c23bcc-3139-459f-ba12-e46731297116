# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_EXPIRES_IN=7d

# Application Environment
NODE_ENV=development
PORT=3000

# Database Configuration (SQLite - no additional config needed)
# The database file will be created automatically in the data/ directory

# Optional: External Services
# SENTRY_DSN=your-sentry-dsn-for-error-tracking
# SLACK_WEBHOOK=your-slack-webhook-for-notifications

# Docker Configuration (for production)
# HOSTNAME=0.0.0.0
