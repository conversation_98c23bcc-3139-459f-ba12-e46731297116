[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\auth\\login\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\auth\\register\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\auth\\verify\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\fetch-case\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\health\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\search-history\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\user\\profile\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\dashboard\\history\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\dashboard\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\register\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\case\\CaseResultDisplay.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\error\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\forms\\CaseSearchForm.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\layout\\DashboardLayout.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\layout\\Header.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\layout\\Sidebar.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\providers\\ThemeProvider.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\contexts\\AuthContext.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\auth.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\database.ts": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\errors.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\utils.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\models\\caseData.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\models\\searchQuery.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\models\\user.ts": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\types\\database.ts": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\__tests__\\auth.test.ts": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\__tests__\\setup.ts": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\__tests__\\utils.test.ts": "32"}, {"size": 2067, "mtime": 1754121428803, "results": "33", "hashOfConfig": "34"}, {"size": 2471, "mtime": 1754121417493, "results": "35", "hashOfConfig": "34"}, {"size": 2539, "mtime": 1754121442066, "results": "36", "hashOfConfig": "34"}, {"size": 6573, "mtime": 1754121842727, "results": "37", "hashOfConfig": "34"}, {"size": 1240, "mtime": 1754122667212, "results": "38", "hashOfConfig": "34"}, {"size": 2619, "mtime": 1754121857887, "results": "39", "hashOfConfig": "34"}, {"size": 4646, "mtime": 1754121878001, "results": "40", "hashOfConfig": "34"}, {"size": 12492, "mtime": 1754122281327, "results": "41", "hashOfConfig": "34"}, {"size": 1957, "mtime": 1754121894020, "results": "42", "hashOfConfig": "34"}, {"size": 1171, "mtime": 1754121695929, "results": "43", "hashOfConfig": "34"}, {"size": 6700, "mtime": 1754121921310, "results": "44", "hashOfConfig": "34"}, {"size": 1152, "mtime": 1754121992559, "results": "45", "hashOfConfig": "34"}, {"size": 11674, "mtime": 1754121962030, "results": "46", "hashOfConfig": "34"}, {"size": 10753, "mtime": 1754121804830, "results": "47", "hashOfConfig": "34"}, {"size": 6873, "mtime": 1754122356864, "results": "48", "hashOfConfig": "34"}, {"size": 9300, "mtime": 1754121750933, "results": "49", "hashOfConfig": "34"}, {"size": 4836, "mtime": 1754121582900, "results": "50", "hashOfConfig": "34"}, {"size": 4363, "mtime": 1754121553145, "results": "51", "hashOfConfig": "34"}, {"size": 5404, "mtime": 1754121531673, "results": "52", "hashOfConfig": "34"}, {"size": 450, "mtime": 1754121507528, "results": "53", "hashOfConfig": "34"}, {"size": 6272, "mtime": 1754121466491, "results": "54", "hashOfConfig": "34"}, {"size": 5956, "mtime": 1754121401089, "results": "55", "hashOfConfig": "34"}, {"size": 3002, "mtime": 1754121281513, "results": "56", "hashOfConfig": "34"}, {"size": 8248, "mtime": 1754122324906, "results": "57", "hashOfConfig": "34"}, {"size": 5142, "mtime": 1754121499360, "results": "58", "hashOfConfig": "34"}, {"size": 6002, "mtime": 1754121366405, "results": "59", "hashOfConfig": "34"}, {"size": 5437, "mtime": 1754121339293, "results": "60", "hashOfConfig": "34"}, {"size": 3130, "mtime": 1754121312788, "results": "61", "hashOfConfig": "34"}, {"size": 2539, "mtime": 1754121297107, "results": "62", "hashOfConfig": "34"}, {"size": 4690, "mtime": 1754122462271, "results": "63", "hashOfConfig": "34"}, {"size": 1550, "mtime": 1754122805340, "results": "64", "hashOfConfig": "34"}, {"size": 4433, "mtime": 1754122438266, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ihyn7r", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\auth\\login\\route.ts", ["162"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\auth\\register\\route.ts", ["163"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\auth\\verify\\route.ts", ["164"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\fetch-case\\route.ts", ["165", "166", "167", "168", "169", "170"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\search-history\\route.ts", ["171", "172"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\api\\user\\profile\\route.ts", ["173", "174", "175", "176", "177"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\dashboard\\history\\page.tsx", ["178"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\login\\page.tsx", ["179", "180"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\app\\register\\page.tsx", ["181"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\case\\CaseResultDisplay.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\error\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\forms\\CaseSearchForm.tsx", ["182"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\layout\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\layout\\Header.tsx", ["183"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\components\\providers\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\contexts\\AuthContext.tsx", ["184"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\auth.ts", ["185", "186", "187"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\errors.ts", ["188", "189", "190", "191", "192", "193", "194", "195"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\lib\\utils.ts", ["196", "197", "198"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\models\\caseData.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\models\\searchQuery.ts", ["199"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\models\\user.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\types\\database.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\__tests__\\auth.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\__tests__\\setup.ts", ["200", "201"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Court Dashboard\\court-dashboard\\src\\__tests__\\utils.test.ts", [], [], {"ruleId": "202", "severity": 1, "message": "203", "line": 56, "column": 13, "nodeType": null, "messageId": "204", "endLine": 56, "endColumn": 26}, {"ruleId": "202", "severity": 1, "message": "203", "line": 65, "column": 13, "nodeType": null, "messageId": "204", "endLine": 65, "endColumn": 26}, {"ruleId": "202", "severity": 1, "message": "203", "line": 36, "column": 13, "nodeType": null, "messageId": "204", "endLine": 36, "endColumn": 26}, {"ruleId": "202", "severity": 1, "message": "205", "line": 5, "column": 26, "nodeType": null, "messageId": "204", "endLine": 5, "endColumn": 42}, {"ruleId": "206", "severity": 1, "message": "207", "line": 8, "column": 98, "nodeType": "208", "messageId": "209", "endLine": 8, "endColumn": 101, "suggestions": "210"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 29, "column": 106, "nodeType": "208", "messageId": "209", "endLine": 29, "endColumn": 109, "suggestions": "211"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 50, "column": 111, "nodeType": "208", "messageId": "209", "endLine": 50, "endColumn": 114, "suggestions": "212"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 90, "column": 30, "nodeType": "208", "messageId": "209", "endLine": 90, "endColumn": 33, "suggestions": "213"}, {"ruleId": "202", "severity": 1, "message": "214", "line": 136, "column": 13, "nodeType": null, "messageId": "204", "endLine": 136, "endColumn": 24}, {"ruleId": "206", "severity": 1, "message": "207", "line": 7, "column": 30, "nodeType": "208", "messageId": "209", "endLine": 7, "endColumn": 33, "suggestions": "215"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 48, "column": 30, "nodeType": "208", "messageId": "209", "endLine": 48, "endColumn": 33, "suggestions": "216"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 7, "column": 30, "nodeType": "208", "messageId": "209", "endLine": 7, "endColumn": 33, "suggestions": "217"}, {"ruleId": "202", "severity": 1, "message": "203", "line": 20, "column": 13, "nodeType": null, "messageId": "204", "endLine": 20, "endColumn": 26}, {"ruleId": "206", "severity": 1, "message": "207", "line": 37, "column": 30, "nodeType": "208", "messageId": "209", "endLine": 37, "endColumn": 33, "suggestions": "218"}, {"ruleId": "202", "severity": 1, "message": "203", "line": 77, "column": 13, "nodeType": null, "messageId": "204", "endLine": 77, "endColumn": 26}, {"ruleId": "206", "severity": 1, "message": "207", "line": 95, "column": 30, "nodeType": "208", "messageId": "209", "endLine": 95, "endColumn": 33, "suggestions": "219"}, {"ruleId": "220", "severity": 1, "message": "221", "line": 66, "column": 6, "nodeType": "222", "endLine": 66, "endColumn": 13, "suggestions": "223"}, {"ruleId": "202", "severity": 1, "message": "224", "line": 43, "column": 14, "nodeType": null, "messageId": "204", "endLine": 43, "endColumn": 19}, {"ruleId": "225", "severity": 2, "message": "226", "line": 184, "column": 18, "nodeType": "227", "messageId": "228", "suggestions": "229"}, {"ruleId": "202", "severity": 1, "message": "224", "line": 96, "column": 14, "nodeType": null, "messageId": "204", "endLine": 96, "endColumn": 19}, {"ruleId": "206", "severity": 1, "message": "207", "line": 90, "column": 66, "nodeType": "208", "messageId": "209", "endLine": 90, "endColumn": 69, "suggestions": "230"}, {"ruleId": "202", "severity": 1, "message": "231", "line": 6, "column": 10, "nodeType": null, "messageId": "204", "endLine": 6, "endColumn": 12}, {"ruleId": "220", "severity": 1, "message": "232", "line": 180, "column": 6, "nodeType": "222", "endLine": 180, "endColumn": 13, "suggestions": "233"}, {"ruleId": "234", "severity": 2, "message": "235", "line": 146, "column": 35, "nodeType": "236", "messageId": "237", "endLine": 146, "endColumn": 43}, {"ruleId": "206", "severity": 1, "message": "207", "line": 147, "column": 22, "nodeType": "208", "messageId": "209", "endLine": 147, "endColumn": 25, "suggestions": "238"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 147, "column": 32, "nodeType": "208", "messageId": "209", "endLine": 147, "endColumn": 35, "suggestions": "239"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 18, "column": 13, "nodeType": "208", "messageId": "209", "endLine": 18, "endColumn": 16, "suggestions": "240"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 25, "column": 29, "nodeType": "208", "messageId": "209", "endLine": 25, "endColumn": 32, "suggestions": "241"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 29, "column": 59, "nodeType": "208", "messageId": "209", "endLine": 29, "endColumn": 62, "suggestions": "242"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 40, "column": 66, "nodeType": "208", "messageId": "209", "endLine": 40, "endColumn": 69, "suggestions": "243"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 56, "column": 68, "nodeType": "208", "messageId": "209", "endLine": 56, "endColumn": 71, "suggestions": "244"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 60, "column": 64, "nodeType": "208", "messageId": "209", "endLine": 60, "endColumn": 67, "suggestions": "245"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 78, "column": 90, "nodeType": "208", "messageId": "209", "endLine": 78, "endColumn": 93, "suggestions": "246"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 141, "column": 41, "nodeType": "208", "messageId": "209", "endLine": 141, "endColumn": 44, "suggestions": "247"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 68, "column": 46, "nodeType": "208", "messageId": "209", "endLine": 68, "endColumn": 49, "suggestions": "248"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 68, "column": 56, "nodeType": "208", "messageId": "209", "endLine": 68, "endColumn": 59, "suggestions": "249"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 92, "column": 35, "nodeType": "208", "messageId": "209", "endLine": 92, "endColumn": 38, "suggestions": "250"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 60, "column": 56, "nodeType": "208", "messageId": "209", "endLine": 60, "endColumn": 59, "suggestions": "251"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 21, "column": 20, "nodeType": "208", "messageId": "209", "endLine": 21, "endColumn": 23, "suggestions": "252"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 31, "column": 43, "nodeType": "208", "messageId": "209", "endLine": 31, "endColumn": 46, "suggestions": "253"}, "@typescript-eslint/no-unused-vars", "'password_hash' is assigned a value but never used.", "unusedVar", "'CaseSearchResult' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["254", "255"], ["256", "257"], ["258", "259"], ["260", "261"], "'rawResponse' is assigned a value but never used.", ["262", "263"], ["264", "265"], ["266", "267"], ["268", "269"], ["270", "271"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchSearchHistory'. Either include it or remove the dependency array.", "ArrayExpression", ["272"], "'error' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["273", "274", "275", "276"], ["277", "278"], "'cn' is defined but never used.", "React Hook useEffect has a missing dependency: 'refreshToken'. Either include it or remove the dependency array.", ["279"], "@typescript-eslint/no-unsafe-function-type", "The `Function` type accepts any function-like value.\nPrefer explicitly defining any function parameters and return type.", "Identifier", "bannedFunctionType", ["280", "281"], ["282", "283"], ["284", "285"], ["286", "287"], ["288", "289"], ["290", "291"], ["292", "293"], ["294", "295"], ["296", "297"], ["298", "299"], ["300", "301"], ["302", "303"], ["304", "305"], ["306", "307"], ["308", "309"], ["310", "311"], {"messageId": "312", "fix": "313", "desc": "314"}, {"messageId": "315", "fix": "316", "desc": "317"}, {"messageId": "312", "fix": "318", "desc": "314"}, {"messageId": "315", "fix": "319", "desc": "317"}, {"messageId": "312", "fix": "320", "desc": "314"}, {"messageId": "315", "fix": "321", "desc": "317"}, {"messageId": "312", "fix": "322", "desc": "314"}, {"messageId": "315", "fix": "323", "desc": "317"}, {"messageId": "312", "fix": "324", "desc": "314"}, {"messageId": "315", "fix": "325", "desc": "317"}, {"messageId": "312", "fix": "326", "desc": "314"}, {"messageId": "315", "fix": "327", "desc": "317"}, {"messageId": "312", "fix": "328", "desc": "314"}, {"messageId": "315", "fix": "329", "desc": "317"}, {"messageId": "312", "fix": "330", "desc": "314"}, {"messageId": "315", "fix": "331", "desc": "317"}, {"messageId": "312", "fix": "332", "desc": "314"}, {"messageId": "315", "fix": "333", "desc": "317"}, {"desc": "334", "fix": "335"}, {"messageId": "336", "data": "337", "fix": "338", "desc": "339"}, {"messageId": "336", "data": "340", "fix": "341", "desc": "342"}, {"messageId": "336", "data": "343", "fix": "344", "desc": "345"}, {"messageId": "336", "data": "346", "fix": "347", "desc": "348"}, {"messageId": "312", "fix": "349", "desc": "314"}, {"messageId": "315", "fix": "350", "desc": "317"}, {"desc": "351", "fix": "352"}, {"messageId": "312", "fix": "353", "desc": "314"}, {"messageId": "315", "fix": "354", "desc": "317"}, {"messageId": "312", "fix": "355", "desc": "314"}, {"messageId": "315", "fix": "356", "desc": "317"}, {"messageId": "312", "fix": "357", "desc": "314"}, {"messageId": "315", "fix": "358", "desc": "317"}, {"messageId": "312", "fix": "359", "desc": "314"}, {"messageId": "315", "fix": "360", "desc": "317"}, {"messageId": "312", "fix": "361", "desc": "314"}, {"messageId": "315", "fix": "362", "desc": "317"}, {"messageId": "312", "fix": "363", "desc": "314"}, {"messageId": "315", "fix": "364", "desc": "317"}, {"messageId": "312", "fix": "365", "desc": "314"}, {"messageId": "315", "fix": "366", "desc": "317"}, {"messageId": "312", "fix": "367", "desc": "314"}, {"messageId": "315", "fix": "368", "desc": "317"}, {"messageId": "312", "fix": "369", "desc": "314"}, {"messageId": "315", "fix": "370", "desc": "317"}, {"messageId": "312", "fix": "371", "desc": "314"}, {"messageId": "315", "fix": "372", "desc": "317"}, {"messageId": "312", "fix": "373", "desc": "314"}, {"messageId": "315", "fix": "374", "desc": "317"}, {"messageId": "312", "fix": "375", "desc": "314"}, {"messageId": "315", "fix": "376", "desc": "317"}, {"messageId": "312", "fix": "377", "desc": "314"}, {"messageId": "315", "fix": "378", "desc": "317"}, {"messageId": "312", "fix": "379", "desc": "314"}, {"messageId": "315", "fix": "380", "desc": "317"}, {"messageId": "312", "fix": "381", "desc": "314"}, {"messageId": "315", "fix": "382", "desc": "317"}, {"messageId": "312", "fix": "383", "desc": "314"}, {"messageId": "315", "fix": "384", "desc": "317"}, "suggestUnknown", {"range": "385", "text": "386"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "387", "text": "388"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "389", "text": "386"}, {"range": "390", "text": "388"}, {"range": "391", "text": "386"}, {"range": "392", "text": "388"}, {"range": "393", "text": "386"}, {"range": "394", "text": "388"}, {"range": "395", "text": "386"}, {"range": "396", "text": "388"}, {"range": "397", "text": "386"}, {"range": "398", "text": "388"}, {"range": "399", "text": "386"}, {"range": "400", "text": "388"}, {"range": "401", "text": "386"}, {"range": "402", "text": "388"}, {"range": "403", "text": "386"}, {"range": "404", "text": "388"}, "Update the dependencies array to be: [fetchSearchHistory, token]", {"range": "405", "text": "406"}, "replaceWithAlt", {"alt": "407"}, {"range": "408", "text": "409"}, "Replace with `&apos;`.", {"alt": "410"}, {"range": "411", "text": "412"}, "Replace with `&lsquo;`.", {"alt": "413"}, {"range": "414", "text": "415"}, "Replace with `&#39;`.", {"alt": "416"}, {"range": "417", "text": "418"}, "Replace with `&rsquo;`.", {"range": "419", "text": "386"}, {"range": "420", "text": "388"}, "Update the dependencies array to be: [refreshToken, token]", {"range": "421", "text": "422"}, {"range": "423", "text": "386"}, {"range": "424", "text": "388"}, {"range": "425", "text": "386"}, {"range": "426", "text": "388"}, {"range": "427", "text": "386"}, {"range": "428", "text": "388"}, {"range": "429", "text": "386"}, {"range": "430", "text": "388"}, {"range": "431", "text": "386"}, {"range": "432", "text": "388"}, {"range": "433", "text": "386"}, {"range": "434", "text": "388"}, {"range": "435", "text": "386"}, {"range": "436", "text": "388"}, {"range": "437", "text": "386"}, {"range": "438", "text": "388"}, {"range": "439", "text": "386"}, {"range": "440", "text": "388"}, {"range": "441", "text": "386"}, {"range": "442", "text": "388"}, {"range": "443", "text": "386"}, {"range": "444", "text": "388"}, {"range": "445", "text": "386"}, {"range": "446", "text": "388"}, {"range": "447", "text": "386"}, {"range": "448", "text": "388"}, {"range": "449", "text": "386"}, {"range": "450", "text": "388"}, {"range": "451", "text": "386"}, {"range": "452", "text": "388"}, {"range": "453", "text": "386"}, {"range": "454", "text": "388"}, [486, 489], "unknown", [486, 489], "never", [1154, 1157], [1154, 1157], [1856, 1859], [1856, 1859], [3076, 3079], [3076, 3079], [253, 256], [253, 256], [1335, 1338], [1335, 1338], [246, 249], [246, 249], [1000, 1003], [1000, 1003], [2589, 2592], [2589, 2592], [1918, 1925], "[fetchSearch<PERSON><PERSON><PERSON>, token]", "&apos;", [6102, 6139], "\n              Don&apos;t have an account?", "&lsquo;", [6102, 6139], "\n              Don&lsquo;t have an account?", "&#39;", [6102, 6139], "\n              Don&#39;t have an account?", "&rsquo;", [6102, 6139], "\n              Don&rsquo;t have an account?", [2418, 2421], [2418, 2421], [4926, 4933], "[refreshToken, token]", [3953, 3956], [3953, 3956], [3963, 3966], [3963, 3966], [514, 517], [514, 517], [669, 672], [669, 672], [805, 808], [805, 808], [1107, 1110], [1107, 1110], [1908, 1911], [1908, 1911], [2105, 2108], [2105, 2108], [2784, 2787], [2784, 2787], [4910, 4913], [4910, 4913], [2096, 2099], [2096, 2099], [2106, 2109], [2106, 2109], [2668, 2671], [2668, 2671], [1877, 1880], [1877, 1880], [467, 470], [467, 470], [670, 673], [670, 673]]