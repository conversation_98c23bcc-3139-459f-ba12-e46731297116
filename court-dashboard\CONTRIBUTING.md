# Contributing to CourtData

Thank you for your interest in contributing to CourtData! This document provides guidelines and information for contributors.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please treat all contributors and users with respect.

## How to Contribute

### Reporting Bugs

1. **Check existing issues** to avoid duplicates
2. **Use the bug report template** when creating new issues
3. **Provide detailed information**:
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if applicable
   - Environment details (OS, browser, Node.js version)

### Suggesting Features

1. **Check existing feature requests** to avoid duplicates
2. **Use the feature request template**
3. **Provide clear use cases** and benefits
4. **Consider implementation complexity**

### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/your-username/court-dashboard.git
   cd court-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env.local
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

### Making Changes

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the existing code style
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**
   ```bash
   npm run test
   npm run lint
   npm run type-check
   ```

4. **Commit your changes**
   ```bash
   git commit -m "feat: add amazing new feature"
   ```
   
   Use conventional commit format:
   - `feat:` for new features
   - `fix:` for bug fixes
   - `docs:` for documentation changes
   - `style:` for formatting changes
   - `refactor:` for code refactoring
   - `test:` for adding tests
   - `chore:` for maintenance tasks

5. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request**
   - Use the PR template
   - Provide clear description of changes
   - Link related issues
   - Request review from maintainers

## Development Guidelines

### Code Style

- **TypeScript**: Use TypeScript for all new code
- **ESLint**: Follow the configured ESLint rules
- **Prettier**: Code formatting is handled automatically
- **Naming**: Use descriptive names for variables and functions
- **Comments**: Add comments for complex logic

### Testing

- **Unit Tests**: Write tests for new functions and components
- **Integration Tests**: Test API endpoints and user flows
- **Coverage**: Maintain test coverage above 80%
- **Test Files**: Place tests in `__tests__` directories or `.test.ts` files

### Documentation

- **README**: Update README for new features
- **API Docs**: Document new API endpoints
- **Code Comments**: Add JSDoc comments for public functions
- **Type Definitions**: Maintain accurate TypeScript types

### Security

- **Input Validation**: Validate all user inputs
- **Authentication**: Follow existing auth patterns
- **Dependencies**: Keep dependencies updated
- **Secrets**: Never commit secrets or API keys

## Project Structure

```
src/
├── app/                 # Next.js app router
│   ├── api/            # API routes
│   ├── dashboard/      # Dashboard pages
│   └── (auth)/         # Auth pages
├── components/         # React components
│   ├── forms/         # Form components
│   ├── layout/        # Layout components
│   └── ui/            # UI components
├── lib/               # Utilities and configs
├── models/            # Database models
├── types/             # TypeScript definitions
└── __tests__/         # Test files
```

## API Guidelines

### Endpoint Design

- Use RESTful conventions
- Include proper HTTP status codes
- Provide consistent error responses
- Add input validation
- Include rate limiting

### Error Handling

```typescript
// Good
return NextResponse.json({
  success: false,
  error: 'User-friendly error message',
  code: 'ERROR_CODE'
}, { status: 400 });

// Bad
throw new Error('Technical error message');
```

### Authentication

- All protected routes must use `withAuth` middleware
- Include proper error handling for auth failures
- Validate JWT tokens properly

## Database Guidelines

### Schema Changes

- Use migrations for schema changes
- Maintain backward compatibility
- Add proper indexes for performance
- Document schema changes

### Queries

- Use parameterized queries to prevent SQL injection
- Optimize queries for performance
- Handle database errors gracefully

## UI/UX Guidelines

### Design System

- Follow the existing design patterns
- Use Tailwind CSS utility classes
- Maintain consistency across components
- Ensure responsive design

### Accessibility

- Include proper ARIA labels
- Maintain keyboard navigation
- Ensure color contrast compliance
- Test with screen readers

### Performance

- Optimize images and assets
- Use lazy loading where appropriate
- Minimize bundle size
- Implement proper caching

## Release Process

1. **Version Bump**: Update version in package.json
2. **Changelog**: Update CHANGELOG.md
3. **Testing**: Run full test suite
4. **Build**: Ensure production build works
5. **Tag**: Create git tag for release
6. **Deploy**: Deploy to production environment

## Getting Help

- **Documentation**: Check the docs/ directory
- **Issues**: Search existing GitHub issues
- **Discussions**: Use GitHub Discussions for questions
- **Discord**: Join our Discord server (link in README)

## Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Annual contributor highlights

Thank you for contributing to CourtData! 🙏
