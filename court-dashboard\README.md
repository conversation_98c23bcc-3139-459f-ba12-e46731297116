# CourtData – <PERSON> Fetcher & Dashboard

[![CI/CD Pipeline](https://github.com/your-username/court-dashboard/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/your-username/court-dashboard/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Next.js](https://img.shields.io/badge/Next.js-000000?logo=next.js&logoColor=white)](https://nextjs.org/)

A **professional full-stack legal web application** that enables authenticated users to retrieve real Indian court case details by searching official court websites. Built with modern web technologies and designed for legal professionals, researchers, and public users seeking reliable Indian court data.

## 🏛️ Features

### 🔐 **Authentication & Security**
- Secure user registration and login
- JWT-based authentication with automatic token refresh
- Password strength validation and bcrypt hashing
- Rate limiting for API endpoints
- Session management with secure storage

### 🔍 **Case Search & Retrieval**
- **Multi-Court Support**: Delhi High Court, Faridabad District Court, and more
- **Comprehensive Search**: Case type, number, filing year, and court selection
- **Real-time Data**: Live case information from official court websites
- **Smart Validation**: Form validation with user-friendly error messages

### 📊 **Case Data Display**
- **Professional Layout**: Government portal-style interface
- **Detailed Information**: Case parties, filing dates, hearing schedules, status
- **Document Access**: Direct PDF download links for orders and judgments
- **Status Tracking**: Visual indicators for case status (Active, Disposed, Pending)

### 📈 **Data Management**
- **Search History**: Track and manage previous searches
- **Export Functionality**: Download search history as CSV
- **Statistics Dashboard**: Success rates and search analytics
- **Data Persistence**: SQLite database with proper relationships

### 🎨 **User Experience**
- **Dark/Light Mode**: System-aware theme switching
- **Responsive Design**: Mobile-first approach with professional aesthetics
- **Loading States**: Smooth loading indicators and error handling
- **Accessibility**: WCAG compliant interface elements

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/court-dashboard.git
   cd court-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` with your configuration:
   ```env
   JWT_SECRET=your-super-secret-jwt-key-change-in-production
   JWT_EXPIRES_IN=7d
   NODE_ENV=development
   ```

4. **Initialize the database**
   ```bash
   npm run dev
   ```
   The SQLite database will be automatically created on first run.

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Authentication**: JWT, bcrypt
- **Database**: SQLite with better-sqlite3
- **Icons**: Lucide React
- **Charts**: Chart.js, react-chartjs-2
- **Testing**: Jest, Playwright
- **Deployment**: Docker, GitHub Actions

### Project Structure
```
court-dashboard/
├── src/
│   ├── app/                    # Next.js app router
│   │   ├── api/               # API routes
│   │   ├── dashboard/         # Dashboard pages
│   │   ├── login/             # Authentication pages
│   │   └── register/
│   ├── components/            # React components
│   │   ├── forms/            # Form components
│   │   ├── layout/           # Layout components
│   │   └── error/            # Error handling
│   ├── contexts/             # React contexts
│   ├── lib/                  # Utilities and configurations
│   ├── models/               # Database models
│   ├── types/                # TypeScript definitions
│   └── __tests__/            # Test files
├── public/                   # Static assets
├── data/                     # SQLite database
├── .github/                  # GitHub Actions workflows
└── docs/                     # Documentation
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Testing
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Generate coverage report

# Code Quality
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking

# Docker
npm run docker:build # Build Docker image
npm run docker:run   # Run Docker container
```

### Database Schema

The application uses SQLite with the following main tables:

- **users**: User accounts and authentication
- **search_queries**: Search history and metadata
- **case_data**: Structured case information
- **raw_responses**: Raw API responses for debugging

### API Endpoints

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Token verification
- `POST /api/auth/verify` - Token refresh

#### Case Management
- `POST /api/fetch-case` - Search for case data
- `GET /api/search-history` - Get user search history
- `DELETE /api/search-history` - Delete search query

#### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `PATCH /api/user/profile` - Change password

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)

1. **Build and run**
   ```bash
   docker-compose up -d
   ```

2. **Access the application**
   - Application: http://localhost:3000
   - With nginx (production): http://localhost

### Manual Docker Build

```bash
# Build the image
docker build -t court-dashboard .

# Run the container
docker run -p 3000:3000 \
  -e JWT_SECRET=your-secret-key \
  -v $(pwd)/data:/app/data \
  court-dashboard
```

## 🚀 Production Deployment

### Environment Variables

```env
# Required
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
NODE_ENV=production

# Optional
JWT_EXPIRES_IN=7d
PORT=3000
```

### Deployment Options

#### 1. **Vercel** (Recommended for Next.js)
```bash
npm install -g vercel
vercel --prod
```

#### 2. **Railway**
```bash
npm install -g @railway/cli
railway login
railway deploy
```

#### 3. **DigitalOcean App Platform**
- Connect your GitHub repository
- Set environment variables
- Deploy automatically

#### 4. **Self-hosted with Docker**
```bash
# Clone on server
git clone https://github.com/your-username/court-dashboard.git
cd court-dashboard

# Set up environment
cp .env.example .env.production
# Edit .env.production with production values

# Deploy with Docker Compose
docker-compose -f docker-compose.yml --profile production up -d
```

## 🧪 Testing

### Unit Tests
```bash
npm run test
```

### Integration Tests
```bash
npm run test:integration
```

### End-to-End Tests
```bash
npx playwright test
```

### Coverage Report
```bash
npm run test:coverage
open coverage/lcov-report/index.html
```

## 🔒 Security Features

- **Password Security**: bcrypt hashing with salt rounds
- **JWT Security**: Secure token generation and validation
- **Rate Limiting**: Prevents brute force attacks
- **Input Validation**: Comprehensive form and API validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Input sanitization and CSP headers
- **CSRF Protection**: Built-in Next.js CSRF protection

## 📊 Supported Courts

Currently integrated with:
- **Delhi High Court** (`https://delhihighcourt.nic.in/web`)
- **Faridabad District Court** (`https://districts.ecourts.gov.in/faridabad`)

**Planned Support:**
- Mumbai High Court
- Kolkata High Court
- Chennai High Court
- Bangalore High Court
- Supreme Court of India

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass: `npm run test`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

### Code Style

- Use TypeScript for all new code
- Follow the existing code style (ESLint + Prettier)
- Write tests for new features
- Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js Team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework
- **Lucide** for the beautiful icons
- **Indian Judiciary** for providing public access to court data

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/court-dashboard/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/court-dashboard/discussions)
- **Email**: <EMAIL>

## 🗺️ Roadmap

### Version 2.0
- [ ] Real-time case status notifications
- [ ] Advanced search filters
- [ ] Case timeline visualization
- [ ] Mobile app (React Native)
- [ ] API rate limiting dashboard
- [ ] Multi-language support

### Version 3.0
- [ ] AI-powered case analysis
- [ ] Bulk case processing
- [ ] Integration with legal databases
- [ ] Advanced analytics and reporting
- [ ] White-label solutions

---

**Built with ❤️ for the Indian Legal Community**

*This application is designed to improve access to public court information and support transparency in the Indian judicial system.*
