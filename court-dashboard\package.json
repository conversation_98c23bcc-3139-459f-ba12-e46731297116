{"name": "court-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@playwright/test": "^1.54.2", "@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "better-sqlite3": "^12.2.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.536.0", "next": "15.4.5", "next-themes": "^0.4.6", "playwright": "^1.54.2", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}