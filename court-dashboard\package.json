{"name": "court-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "docker:build": "docker build -t court-dashboard .", "docker:run": "docker run -p 3000:3000 court-dashboard"}, "dependencies": {"@headlessui/react": "^2.2.7", "@playwright/test": "^1.54.2", "@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "better-sqlite3": "^12.2.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.536.0", "next": "15.4.5", "next-themes": "^0.4.6", "playwright": "^1.54.2", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "ts-jest": "^29.1.2", "typescript": "^5"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/__tests__/setup.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/__tests__/**/*"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{ts,tsx}", "<rootDir>/src/**/*.{test,spec}.{ts,tsx}"]}}