import { AuthService } from '@/lib/auth';

// Mock JWT for testing
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(() => 'mock-token'),
  verify: jest.fn(() => ({ userId: 1, email: '<EMAIL>', name: 'Test User' })),
  decode: jest.fn(() => ({ userId: 1, email: '<EMAIL>', name: 'Test User', exp: Date.now() / 1000 + 3600 }))
}));

describe('AuthService', () => {
  const mockUser = {
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    password_hash: 'hashed-password',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  };

  describe('generateToken', () => {
    it('should generate a token for valid user', () => {
      const token = AuthService.generateToken(mockUser);
      expect(token).toBe('mock-token');
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', () => {
      const payload = AuthService.verifyToken('valid-token');
      expect(payload).toEqual({
        userId: 1,
        email: '<EMAIL>',
        name: 'Test User'
      });
    });
  });

  describe('extractTokenFromHeader', () => {
    it('should extract token from Bearer header', () => {
      const token = AuthService.extractTokenFromHeader('Bearer abc123');
      expect(token).toBe('abc123');
    });

    it('should return null for invalid header format', () => {
      expect(AuthService.extractTokenFromHeader('Invalid header')).toBeNull();
      expect(AuthService.extractTokenFromHeader('Bearer')).toBeNull();
      expect(AuthService.extractTokenFromHeader(null)).toBeNull();
    });
  });

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(AuthService.isValidEmail('<EMAIL>')).toBe(true);
      expect(AuthService.isValidEmail('<EMAIL>')).toBe(true);
      expect(AuthService.isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(AuthService.isValidEmail('invalid-email')).toBe(false);
      expect(AuthService.isValidEmail('test@')).toBe(false);
      expect(AuthService.isValidEmail('@example.com')).toBe(false);
      expect(AuthService.isValidEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('isValidPassword', () => {
    it('should validate strong passwords', () => {
      const result = AuthService.isValidPassword('StrongPass123!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject weak passwords', () => {
      const result = AuthService.isValidPassword('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should require minimum length', () => {
      const result = AuthService.isValidPassword('Short1!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should require uppercase letter', () => {
      const result = AuthService.isValidPassword('lowercase123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('should require lowercase letter', () => {
      const result = AuthService.isValidPassword('UPPERCASE123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('should require number', () => {
      const result = AuthService.isValidPassword('NoNumbers!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('should require special character', () => {
      const result = AuthService.isValidPassword('NoSpecial123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one special character');
    });
  });

  describe('generateSessionId', () => {
    it('should generate unique session IDs', () => {
      const id1 = AuthService.generateSessionId();
      const id2 = AuthService.generateSessionId();
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(10);
    });
  });

  describe('hashSessionId', () => {
    it('should hash session ID consistently', () => {
      const sessionId = 'test-session-id';
      const hash1 = AuthService.hashSessionId(sessionId);
      const hash2 = AuthService.hashSessionId(sessionId);
      
      expect(hash1).toBe(hash2);
      expect(hash1).not.toBe(sessionId);
    });
  });
});
