import {
  formatDate,
  formatCaseNumber,
  getCaseStatusColor,
  isValidCaseNumber,
  getFilingYears,
  truncateText,
  isValidUrl
} from '@/lib/utils';

describe('Utils', () => {
  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = '2023-12-25';
      const formatted = formatDate(date);
      expect(formatted).toBe('25 December 2023');
    });

    it('should handle Date object', () => {
      const date = new Date('2023-12-25');
      const formatted = formatDate(date);
      expect(formatted).toBe('25 December 2023');
    });
  });

  describe('formatCaseNumber', () => {
    it('should format case number correctly', () => {
      const result = formatCaseNumber('CIVIL', '12345', 2023);
      expect(result).toBe('CIVIL/12345/2023');
    });

    it('should handle different case types', () => {
      const result = formatCaseNumber('CRL', 'ABC123', 2022);
      expect(result).toBe('CRL/ABC123/2022');
    });
  });

  describe('getCaseStatusColor', () => {
    it('should return correct color for active status', () => {
      const color = getCaseStatusColor('active');
      expect(color).toContain('yellow');
    });

    it('should return correct color for disposed status', () => {
      const color = getCaseStatusColor('disposed');
      expect(color).toContain('green');
    });

    it('should return correct color for dismissed status', () => {
      const color = getCaseStatusColor('dismissed');
      expect(color).toContain('red');
    });

    it('should return default color for unknown status', () => {
      const color = getCaseStatusColor('unknown');
      expect(color).toContain('gray');
    });

    it('should be case insensitive', () => {
      const color1 = getCaseStatusColor('ACTIVE');
      const color2 = getCaseStatusColor('active');
      expect(color1).toBe(color2);
    });
  });

  describe('isValidCaseNumber', () => {
    it('should validate correct case numbers', () => {
      expect(isValidCaseNumber('12345')).toBe(true);
      expect(isValidCaseNumber('ABC-123')).toBe(true);
      expect(isValidCaseNumber('CASE/2023')).toBe(true);
      expect(isValidCaseNumber('123ABC')).toBe(true);
    });

    it('should reject invalid case numbers', () => {
      expect(isValidCaseNumber('')).toBe(false);
      expect(isValidCaseNumber('case with spaces')).toBe(false);
      expect(isValidCaseNumber('case@123')).toBe(false);
      expect(isValidCaseNumber('a'.repeat(51))).toBe(false);
    });
  });

  describe('getFilingYears', () => {
    it('should return array of years', () => {
      const years = getFilingYears();
      const currentYear = new Date().getFullYear();
      
      expect(Array.isArray(years)).toBe(true);
      expect(years).toContain(currentYear);
      expect(years).toContain(currentYear - 1);
      expect(years.length).toBe(21); // Current year + 20 previous years
    });

    it('should be in descending order', () => {
      const years = getFilingYears();
      for (let i = 0; i < years.length - 1; i++) {
        expect(years[i]).toBeGreaterThan(years[i + 1]);
      }
    });
  });

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const text = 'This is a very long text that should be truncated';
      const result = truncateText(text, 20);
      expect(result).toBe('This is a very long ...');
      expect(result.length).toBe(23); // 20 + '...'
    });

    it('should not truncate short text', () => {
      const text = 'Short text';
      const result = truncateText(text, 20);
      expect(result).toBe('Short text');
    });

    it('should handle exact length', () => {
      const text = 'Exactly twenty chars';
      const result = truncateText(text, 20);
      expect(result).toBe('Exactly twenty chars');
    });
  });

  describe('isValidUrl', () => {
    it('should validate correct URLs', () => {
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://example.com')).toBe(true);
      expect(isValidUrl('https://sub.example.com/path')).toBe(true);
      expect(isValidUrl('https://example.com:8080')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('ftp://example.com')).toBe(true); // FTP is valid URL
      expect(isValidUrl('')).toBe(false);
      expect(isValidUrl('just-text')).toBe(false);
    });
  });
});
