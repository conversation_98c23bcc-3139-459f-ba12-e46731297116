import { NextRequest, NextResponse } from 'next/server';
import { UserModel } from '@/models/user';
import { AuthService, RateLimiter } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Input validation
    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // Validate email format
    if (!AuthService.isValidEmail(email)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email format'
      }, { status: 400 });
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const rateLimit = RateLimiter.checkRateLimit(`login:${clientIP}`, 5, 15 * 60 * 1000);
    if (!rateLimit.allowed) {
      return NextResponse.json({
        success: false,
        error: 'Too many login attempts. Please try again later.',
        resetTime: rateLimit.resetTime
      }, { status: 429 });
    }

    // Authenticate user
    const user = await UserModel.authenticate(email, password);
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email or password'
      }, { status: 401 });
    }

    // Generate JWT token
    const token = AuthService.generateToken(user);

    // Reset rate limit on successful login
    RateLimiter.resetRateLimit(`login:${clientIP}`);

    // Return success response (exclude password hash)
    const { password_hash, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userWithoutPassword,
        token
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
