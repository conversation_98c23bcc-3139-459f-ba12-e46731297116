import { NextRequest, NextResponse } from 'next/server';
import { UserModel } from '@/models/user';
import { AuthService, RateLimiter } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, password } = body;

    // Input validation
    if (!name || !email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Name, email, and password are required'
      }, { status: 400 });
    }

    // Validate email format
    if (!AuthService.isValidEmail(email)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email format'
      }, { status: 400 });
    }

    // Validate password strength
    const passwordValidation = AuthService.isValidPassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Password does not meet requirements',
        details: passwordValidation.errors
      }, { status: 400 });
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const rateLimit = RateLimiter.checkRateLimit(`register:${clientIP}`, 3, 15 * 60 * 1000);
    if (!rateLimit.allowed) {
      return NextResponse.json({
        success: false,
        error: 'Too many registration attempts. Please try again later.',
        resetTime: rateLimit.resetTime
      }, { status: 429 });
    }

    // Check if user already exists
    if (UserModel.emailExists(email)) {
      return NextResponse.json({
        success: false,
        error: 'User with this email already exists'
      }, { status: 409 });
    }

    // Create user with hashed password
    const user = await UserModel.createWithHashedPassword(name, email, password);

    // Generate JWT token
    const token = AuthService.generateToken(user);

    // Return success response (exclude password hash)
    const { password_hash, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userWithoutPassword,
        token
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
