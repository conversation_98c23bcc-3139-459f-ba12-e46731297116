import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { UserModel } from '@/models/user';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = AuthService.extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'No token provided'
      }, { status: 401 });
    }

    // Verify token
    const payload = AuthService.verifyToken(token);
    if (!payload) {
      return NextResponse.json({
        success: false,
        error: 'Invalid or expired token'
      }, { status: 401 });
    }

    // Get fresh user data from database
    const user = UserModel.findById(payload.userId);
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Return user data (exclude password hash)
    const { password_hash, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      data: {
        user: userWithoutPassword,
        tokenValid: true
      }
    });

  } catch (error) {
    console.error('Token verification error:', error);
    return NextResponse.json({
      success: false,
      error: 'Token verification failed'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Token is required'
      }, { status: 400 });
    }

    // Check if token is expired and refresh if needed
    if (AuthService.isTokenExpired(token)) {
      const newToken = AuthService.refreshToken(token);
      if (!newToken) {
        return NextResponse.json({
          success: false,
          error: 'Unable to refresh token'
        }, { status: 401 });
      }

      return NextResponse.json({
        success: true,
        message: 'Token refreshed',
        data: {
          token: newToken,
          refreshed: true
        }
      });
    }

    // Token is still valid
    return NextResponse.json({
      success: true,
      message: 'Token is valid',
      data: {
        token,
        refreshed: false
      }
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json({
      success: false,
      error: 'Token refresh failed'
    }, { status: 500 });
  }
}
