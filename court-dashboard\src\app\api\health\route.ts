import { NextResponse } from 'next/server';
import db from '@/lib/database';

export async function GET() {
  try {
    // Check database connectivity
    const dbCheck = db.prepare('SELECT 1 as test').get();
    
    // Get basic stats
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
    const queryCount = db.prepare('SELECT COUNT(*) as count FROM search_queries').get() as { count: number };
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: {
        status: dbCheck ? 'connected' : 'disconnected',
        users: userCount.count,
        queries: queryCount.count
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      node_version: process.version
    };

    return NextResponse.json(healthData);
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      uptime: process.uptime()
    }, { status: 503 });
  }
}
