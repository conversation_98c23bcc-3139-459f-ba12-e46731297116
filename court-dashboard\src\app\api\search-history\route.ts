import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth';
import { SearchQueryModel } from '@/models/searchQuery';

async function handleGetSearchHistory(request: NextRequest) {
  try {
    const user = (request as any).user;
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Get search history with case data
    const searchHistory = SearchQueryModel.getSearchHistoryWithCaseData(
      user.userId,
      limit,
      offset
    );

    // Get user search statistics
    const stats = SearchQueryModel.getUserSearchStats(user.userId);

    return NextResponse.json({
      success: true,
      data: {
        queries: searchHistory,
        stats,
        pagination: {
          page,
          limit,
          total: stats.total,
          totalPages: Math.ceil(stats.total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Search history error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve search history'
    }, { status: 500 });
  }
}

async function handleDeleteSearchQuery(request: NextRequest) {
  try {
    const user = (request as any).user;
    const body = await request.json();
    const { queryId } = body;

    if (!queryId) {
      return NextResponse.json({
        success: false,
        error: 'Query ID is required'
      }, { status: 400 });
    }

    // Verify the query belongs to the user
    const query = SearchQueryModel.findById(queryId);
    if (!query || query.user_id !== user.userId) {
      return NextResponse.json({
        success: false,
        error: 'Query not found or access denied'
      }, { status: 404 });
    }

    // Delete the query (cascade will handle related records)
    const deleted = SearchQueryModel.delete(queryId);
    
    if (deleted) {
      return NextResponse.json({
        success: true,
        message: 'Search query deleted successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to delete search query'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Delete search query error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete search query'
    }, { status: 500 });
  }
}

// Export handlers with authentication
export const GET = withAuth(handleGetSearchHistory);
export const DELETE = withAuth(handleDeleteSearchQuery);
