import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthService } from '@/lib/auth';
import { UserModel } from '@/models/user';

async function handleGetProfile(request: NextRequest) {
  try {
    const user = (request as any).user;
    
    // Get fresh user data from database
    const userData = UserModel.findById(user.userId);
    
    if (!userData) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Return user data without password hash
    const { password_hash, ...userWithoutPassword } = userData;

    return NextResponse.json({
      success: true,
      data: userWithoutPassword
    });
  } catch (error) {
    console.error('Get profile error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve profile'
    }, { status: 500 });
  }
}

async function handleUpdateProfile(request: NextRequest) {
  try {
    const user = (request as any).user;
    const body = await request.json();
    const { name, email } = body;

    // Input validation
    if (!name || !email) {
      return NextResponse.json({
        success: false,
        error: 'Name and email are required'
      }, { status: 400 });
    }

    // Validate email format
    if (!AuthService.isValidEmail(email)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email format'
      }, { status: 400 });
    }

    // Check if email is already taken by another user
    const existingUser = UserModel.findByEmail(email);
    if (existingUser && existingUser.id !== user.userId) {
      return NextResponse.json({
        success: false,
        error: 'Email is already taken'
      }, { status: 409 });
    }

    // Update user profile
    const updatedUser = UserModel.update(user.userId, { name, email });
    
    if (!updatedUser) {
      return NextResponse.json({
        success: false,
        error: 'Failed to update profile'
      }, { status: 500 });
    }

    // Return updated user data without password hash
    const { password_hash, ...userWithoutPassword } = updatedUser;

    return NextResponse.json({
      success: true,
      data: userWithoutPassword,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update profile'
    }, { status: 500 });
  }
}

async function handleChangePassword(request: NextRequest) {
  try {
    const user = (request as any).user;
    const body = await request.json();
    const { currentPassword, newPassword } = body;

    // Input validation
    if (!currentPassword || !newPassword) {
      return NextResponse.json({
        success: false,
        error: 'Current password and new password are required'
      }, { status: 400 });
    }

    // Validate new password strength
    const passwordValidation = AuthService.isValidPassword(newPassword);
    if (!passwordValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'New password does not meet requirements',
        details: passwordValidation.errors
      }, { status: 400 });
    }

    // Get user data
    const userData = UserModel.findById(user.userId);
    if (!userData) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Verify current password
    const isCurrentPasswordValid = await UserModel.verifyPassword(currentPassword, userData.password_hash);
    if (!isCurrentPasswordValid) {
      return NextResponse.json({
        success: false,
        error: 'Current password is incorrect'
      }, { status: 401 });
    }

    // Hash new password and update
    const hashedNewPassword = await UserModel.hashPassword(newPassword);
    const updatedUser = UserModel.update(user.userId, { password_hash: hashedNewPassword });
    
    if (!updatedUser) {
      return NextResponse.json({
        success: false,
        error: 'Failed to update password'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to change password'
    }, { status: 500 });
  }
}

// Export handlers with authentication
export const GET = withAuth(handleGetProfile);
export const PUT = withAuth(handleUpdateProfile);
export const PATCH = withAuth(handleChangePassword);
