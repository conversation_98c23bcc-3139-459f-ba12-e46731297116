'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout, Card, Loading, EmptyState } from '@/components/layout/DashboardLayout';
import { PageHeader } from '@/components/layout/Header';
import { withAuth, useAuth } from '@/contexts/AuthContext';
import { SearchHistoryItem } from '@/types/database';
import { formatDate, formatCaseNumber, getCaseStatusColor, downloadCSV } from '@/lib/utils';
import { 
  History, 
  Download, 
  Trash2, 
  Eye, 
  Calendar,
  FileText,
  BarChart3,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchStats {
  total: number;
  successful: number;
  failed: number;
  pending: number;
}

function SearchHistoryPage() {
  const { token } = useAuth();
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [stats, setStats] = useState<SearchStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchSearchHistory = async (pageNum = 1) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/search-history?page=${pageNum}&limit=20`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();

      if (result.success) {
        setSearchHistory(result.data.queries);
        setStats(result.data.stats);
        setTotalPages(result.data.pagination.totalPages);
        setPage(pageNum);
      } else {
        setError(result.error || 'Failed to fetch search history');
      }
    } catch (err) {
      console.error('Search history error:', err);
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSearchHistory();
  }, [token]);

  const handleDeleteQuery = async (queryId: number) => {
    if (!confirm('Are you sure you want to delete this search query?')) {
      return;
    }

    try {
      const response = await fetch('/api/search-history', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ queryId })
      });

      const result = await response.json();

      if (result.success) {
        // Refresh the search history
        fetchSearchHistory(page);
      } else {
        alert(result.error || 'Failed to delete search query');
      }
    } catch (err) {
      console.error('Delete error:', err);
      alert('Network error. Please try again.');
    }
  };

  const handleExportCSV = () => {
    if (searchHistory.length === 0) return;

    const csvData = searchHistory.map(item => ({
      'Case Number': formatCaseNumber(item.case_type, item.case_number, item.filing_year),
      'Court': item.court,
      'Case Type': item.case_type,
      'Filing Year': item.filing_year,
      'Search Date': formatDate(item.search_timestamp),
      'Status': item.status,
      'Petitioner': item.case_data?.petitioner || 'N/A',
      'Respondent': item.case_data?.respondent || 'N/A',
      'Case Status': item.case_data?.case_status || 'N/A'
    }));

    downloadCSV(csvData, `search-history-${new Date().toISOString().split('T')[0]}`);
  };

  if (isLoading && searchHistory.length === 0) {
    return (
      <DashboardLayout title="Search History" subtitle="View and manage your case search history">
        <Loading />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Search History" subtitle="View and manage your case search history">
      <PageHeader
        title="Search History"
        subtitle="Track your case searches and access previous results"
        actions={
          <div className="flex space-x-3">
            <button
              onClick={handleExportCSV}
              disabled={searchHistory.length === 0}
              className="btn-secondary flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </button>
          </div>
        }
      />

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card>
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Search className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Searches</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total}</p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <FileText className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Successful</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.successful}</p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                <BarChart3 className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Failed</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.failed}</p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <History className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.pending}</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Search History Table */}
      <Card title="Search History" subtitle="Your recent case searches">
        {error ? (
          <div className="text-center py-8">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        ) : searchHistory.length === 0 ? (
          <EmptyState
            icon={<History className="h-12 w-12" />}
            title="No search history"
            description="You haven't performed any case searches yet. Start by searching for a case."
          />
        ) : (
          <div className="overflow-x-auto">
            <table className="table">
              <thead className="table-header">
                <tr>
                  <th className="table-header-cell">Case Details</th>
                  <th className="table-header-cell">Court</th>
                  <th className="table-header-cell">Search Date</th>
                  <th className="table-header-cell">Status</th>
                  <th className="table-header-cell">Case Status</th>
                  <th className="table-header-cell">Actions</th>
                </tr>
              </thead>
              <tbody className="table-body">
                {searchHistory.map((item) => {
                  const caseNumber = formatCaseNumber(item.case_type, item.case_number, item.filing_year);
                  
                  return (
                    <tr key={item.id}>
                      <td className="table-cell">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white font-mono">
                            {caseNumber}
                          </p>
                          {item.case_data && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {item.case_data.petitioner} vs {item.case_data.respondent}
                            </p>
                          )}
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {item.court}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(item.search_timestamp)}
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className={cn(
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          item.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                          item.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                        )}>
                          {item.status}
                        </span>
                      </td>
                      <td className="table-cell">
                        {item.case_data?.case_status ? (
                          <span className={cn(
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            getCaseStatusColor(item.case_data.case_status)
                          )}>
                            {item.case_data.case_status}
                          </span>
                        ) : (
                          <span className="text-gray-400 dark:text-gray-500">N/A</span>
                        )}
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          {item.case_data && (
                            <button
                              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                              title="View case details"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteQuery(item.id)}
                            className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                            title="Delete search"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Page {page} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => fetchSearchHistory(page - 1)}
                disabled={page <= 1}
                className="btn-secondary disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => fetchSearchHistory(page + 1)}
                disabled={page >= totalPages}
                className="btn-secondary disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </Card>
    </DashboardLayout>
  );
}

export default withAuth(SearchHistoryPage);
