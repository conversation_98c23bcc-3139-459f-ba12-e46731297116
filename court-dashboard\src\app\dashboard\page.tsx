'use client';

import React, { useState } from 'react';
import { DashboardLayout, Grid } from '@/components/layout/DashboardLayout';
import { CaseSearchForm } from '@/components/forms/CaseSearchForm';
import { CaseResultDisplay } from '@/components/case/CaseResultDisplay';
import { withAuth, useAuth } from '@/contexts/AuthContext';
import { SearchFormData, CaseData } from '@/types/database';

function DashboardPage() {
  const { token } = useAuth();
  const [caseData, setCaseData] = useState<CaseData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleCaseSearch = async (formData: SearchFormData) => {
    setIsLoading(true);
    setError(null);
    setCaseData(null);

    try {
      const response = await fetch('/api/fetch-case', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        setCaseData(result.data);
      } else {
        setError(result.error || 'Failed to fetch case data');
      }
    } catch (err) {
      console.error('Search error:', err);
      setError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout 
      title="Case Search" 
      subtitle="Search for court cases and view detailed information"
    >
      <Grid cols={2} gap={6}>
        {/* Search Form */}
        <CaseSearchForm 
          onSubmit={handleCaseSearch}
          isLoading={isLoading}
        />

        {/* Case Results */}
        <CaseResultDisplay 
          caseData={caseData}
          isLoading={isLoading}
          error={error}
        />
      </Grid>
    </DashboardLayout>
  );
}

export default withAuth(DashboardPage);
