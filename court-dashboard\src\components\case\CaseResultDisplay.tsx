'use client';

import React from 'react';
import { 
  FileText, 
  Download, 
  Calendar, 
  Scale, 
  User, 
  Clock,
  ExternalLink,
  Copy,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { CaseData } from '@/types/database';
import { formatDate, formatCaseNumber, getCaseStatusColor, copyToClipboard } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface CaseResultDisplayProps {
  caseData: CaseData | null;
  isLoading?: boolean;
  error?: string;
  className?: string;
}

export function CaseResultDisplay({ caseData, isLoading, error, className }: CaseResultDisplayProps) {
  const [copied, setCopied] = React.useState(false);

  // Handle copy case number
  const handleCopyCaseNumber = async () => {
    if (!caseData) return;
    
    const caseNumber = formatCaseNumber(caseData.case_type, caseData.case_number, parseInt(caseData.filing_date?.split('-')[0] || '2023'));
    const success = await copyToClipboard(caseNumber);
    
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Handle PDF download
  const handleDownloadPDF = () => {
    if (caseData?.latest_order_url) {
      window.open(caseData.latest_order_url, '_blank');
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'disposed':
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'dismissed':
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className={cn("bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm", className)}>
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Scale className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
            Case Details
          </h2>
        </div>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm", className)}>
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Scale className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
            Case Details
          </h2>
        </div>
        <div className="p-6">
          <div className="text-center py-8">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Search Failed
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {error}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!caseData) {
    return (
      <div className={cn("bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm", className)}>
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Scale className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
            Case Details
          </h2>
        </div>
        <div className="p-6">
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Case Selected
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Use the search form to find and display case details
            </p>
          </div>
        </div>
      </div>
    );
  }

  const filingYear = caseData.filing_date ? new Date(caseData.filing_date).getFullYear() : new Date().getFullYear();
  const fullCaseNumber = formatCaseNumber(caseData.case_type, caseData.case_number, filingYear);

  return (
    <div className={cn("bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm", className)}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Scale className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
            Case Details
          </h2>
          {caseData.latest_order_url && (
            <button
              onClick={handleDownloadPDF}
              className="btn-secondary text-sm flex items-center"
            >
              <Download className="h-4 w-4 mr-1" />
              Download PDF
            </button>
          )}
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Case Header */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-xl font-bold text-blue-900 dark:text-blue-100 font-mono">
              {fullCaseNumber}
            </h3>
            <button
              onClick={handleCopyCaseNumber}
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              title="Copy case number"
            >
              {copied ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </button>
          </div>
          <div className="flex items-center space-x-4">
            <span className={cn(
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              getCaseStatusColor(caseData.case_status || 'Unknown')
            )}>
              {getStatusIcon(caseData.case_status || 'Unknown')}
              <span className="ml-1">{caseData.case_status || 'Unknown'}</span>
            </span>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {caseData.court_name}
            </span>
          </div>
        </div>

        {/* Case Parties */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <User className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
              <h4 className="font-medium text-gray-900 dark:text-white">Petitioner</h4>
            </div>
            <p className="text-gray-700 dark:text-gray-300">
              {caseData.petitioner || 'Not available'}
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <User className="h-4 w-4 text-red-600 dark:text-red-400 mr-2" />
              <h4 className="font-medium text-gray-900 dark:text-white">Respondent</h4>
            </div>
            <p className="text-gray-700 dark:text-gray-300">
              {caseData.respondent || 'Not available'}
            </p>
          </div>
        </div>

        {/* Case Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3">
            <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Filing Date</p>
              <p className="text-gray-600 dark:text-gray-400">
                {caseData.filing_date ? formatDate(caseData.filing_date) : 'Not available'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Next Hearing</p>
              <p className="text-gray-600 dark:text-gray-400">
                {caseData.next_hearing_date ? formatDate(caseData.next_hearing_date) : 'Not scheduled'}
              </p>
            </div>
          </div>
        </div>

        {/* Latest Order */}
        {caseData.latest_order_url && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FileText className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Latest Order/Judgment</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Click to view or download the latest court order
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownloadPDF}
                className="text-yellow-600 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300"
              >
                <ExternalLink className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* Case Summary */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Case Summary</h4>
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <p>• Case Type: {caseData.case_type}</p>
            <p>• Court: {caseData.court_name}</p>
            <p>• Status: {caseData.case_status || 'Unknown'}</p>
            <p>• Last Updated: {formatDate(caseData.created_at)}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
