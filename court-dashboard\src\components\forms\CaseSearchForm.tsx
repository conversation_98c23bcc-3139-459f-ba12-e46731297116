'use client';

import React, { useState } from 'react';
import { Search, Loader2, AlertCircle } from 'lucide-react';
import { COURTS, CASE_TYPES, SearchFormData } from '@/types/database';
import { getFilingYears, isValidCaseNumber } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface CaseSearchFormProps {
  onSubmit: (data: SearchFormData) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

interface FormErrors {
  court?: string;
  case_type?: string;
  case_number?: string;
  filing_year?: string;
}

export function CaseSearchForm({ onSubmit, isLoading = false, className }: CaseSearchFormProps) {
  const [formData, setFormData] = useState<SearchFormData>({
    court: 'Delhi High Court',
    case_type: 'CIVIL',
    case_number: '',
    filing_year: new Date().getFullYear()
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const filingYears = getFilingYears();

  // Validate form data
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.court) {
      newErrors.court = 'Please select a court';
    }

    if (!formData.case_type) {
      newErrors.case_type = 'Please select a case type';
    }

    if (!formData.case_number.trim()) {
      newErrors.case_number = 'Case number is required';
    } else if (!isValidCaseNumber(formData.case_number.trim())) {
      newErrors.case_number = 'Invalid case number format';
    }

    if (!formData.filing_year) {
      newErrors.filing_year = 'Please select a filing year';
    } else if (formData.filing_year > new Date().getFullYear()) {
      newErrors.filing_year = 'Filing year cannot be in the future';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    setTouched({
      court: true,
      case_type: true,
      case_number: true,
      filing_year: true
    });

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        ...formData,
        case_number: formData.case_number.trim()
      });
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  // Handle field changes
  const handleFieldChange = (field: keyof SearchFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Handle field blur
  const handleFieldBlur = (field: keyof SearchFormData) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateForm();
  };

  return (
    <div className={cn("bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm", className)}>
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <Search className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
          Case Search
        </h2>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Search for court cases by entering the case details below
        </p>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Court Selection */}
        <div>
          <label htmlFor="court" className="form-label">
            Court <span className="text-red-500">*</span>
          </label>
          <select
            id="court"
            value={formData.court}
            onChange={(e) => handleFieldChange('court', e.target.value)}
            onBlur={() => handleFieldBlur('court')}
            className={cn(
              "form-input",
              errors.court && touched.court && "border-red-500 focus:border-red-500 focus:ring-red-500"
            )}
            disabled={isLoading}
          >
            {COURTS.map((court) => (
              <option key={court} value={court}>
                {court}
              </option>
            ))}
          </select>
          {errors.court && touched.court && (
            <div className="form-error flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              {errors.court}
            </div>
          )}
        </div>

        {/* Case Type Selection */}
        <div>
          <label htmlFor="case_type" className="form-label">
            Case Type <span className="text-red-500">*</span>
          </label>
          <select
            id="case_type"
            value={formData.case_type}
            onChange={(e) => handleFieldChange('case_type', e.target.value)}
            onBlur={() => handleFieldBlur('case_type')}
            className={cn(
              "form-input",
              errors.case_type && touched.case_type && "border-red-500 focus:border-red-500 focus:ring-red-500"
            )}
            disabled={isLoading}
          >
            {CASE_TYPES.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
          {errors.case_type && touched.case_type && (
            <div className="form-error flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              {errors.case_type}
            </div>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            CIVIL: Civil cases, CRL: Criminal cases, WP: Writ Petitions, etc.
          </p>
        </div>

        {/* Case Number Input */}
        <div>
          <label htmlFor="case_number" className="form-label">
            Case Number <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="case_number"
            value={formData.case_number}
            onChange={(e) => handleFieldChange('case_number', e.target.value)}
            onBlur={() => handleFieldBlur('case_number')}
            placeholder="e.g., 12345, ABC-123, etc."
            className={cn(
              "form-input",
              errors.case_number && touched.case_number && "border-red-500 focus:border-red-500 focus:ring-red-500"
            )}
            disabled={isLoading}
          />
          {errors.case_number && touched.case_number && (
            <div className="form-error flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              {errors.case_number}
            </div>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Enter the case number without the case type prefix or year
          </p>
        </div>

        {/* Filing Year Selection */}
        <div>
          <label htmlFor="filing_year" className="form-label">
            Filing Year <span className="text-red-500">*</span>
          </label>
          <select
            id="filing_year"
            value={formData.filing_year}
            onChange={(e) => handleFieldChange('filing_year', parseInt(e.target.value))}
            onBlur={() => handleFieldBlur('filing_year')}
            className={cn(
              "form-input",
              errors.filing_year && touched.filing_year && "border-red-500 focus:border-red-500 focus:ring-red-500"
            )}
            disabled={isLoading}
          >
            {filingYears.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          {errors.filing_year && touched.filing_year && (
            <div className="form-error flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              {errors.filing_year}
            </div>
          )}
        </div>

        {/* Case Preview */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Case Preview:
          </h3>
          <p className="text-lg font-mono text-blue-600 dark:text-blue-400">
            {formData.case_type}/{formData.case_number || '___'}/{formData.filing_year}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {formData.court}
          </p>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading}
          className="btn-primary w-full flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Searching...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Search Case
            </>
          )}
        </button>

        {/* Help Text */}
        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
          <p>• Ensure you have the correct case number and filing year</p>
          <p>• Case search may take a few moments to complete</p>
          <p>• Results will be displayed in the case details section</p>
        </div>
      </form>
    </div>
  );
}
