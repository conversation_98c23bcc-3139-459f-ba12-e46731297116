import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Database file path
const dbPath = path.join(process.cwd(), 'data', 'courtdata.db');

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Initialize database
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Database schema initialization
export function initializeDatabase() {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Search queries table
  db.exec(`
    CREATE TABLE IF NOT EXISTS search_queries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      court TEXT NOT NULL,
      case_type TEXT NOT NULL,
      case_number TEXT NOT NULL,
      filing_year INTEGER NOT NULL,
      search_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      status TEXT DEFAULT 'pending',
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);

  // Raw responses table for logging
  db.exec(`
    CREATE TABLE IF NOT EXISTS raw_responses (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      query_id INTEGER NOT NULL,
      raw_html TEXT,
      raw_text TEXT,
      response_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      success BOOLEAN DEFAULT FALSE,
      error_message TEXT,
      FOREIGN KEY (query_id) REFERENCES search_queries (id) ON DELETE CASCADE
    )
  `);

  // Case data table for structured case information
  db.exec(`
    CREATE TABLE IF NOT EXISTS case_data (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      query_id INTEGER NOT NULL,
      case_number TEXT NOT NULL,
      case_type TEXT NOT NULL,
      court_name TEXT NOT NULL,
      petitioner TEXT,
      respondent TEXT,
      filing_date DATE,
      next_hearing_date DATE,
      case_status TEXT,
      latest_order_url TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (query_id) REFERENCES search_queries (id) ON DELETE CASCADE
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users (email);
    CREATE INDEX IF NOT EXISTS idx_search_queries_user_id ON search_queries (user_id);
    CREATE INDEX IF NOT EXISTS idx_search_queries_timestamp ON search_queries (search_timestamp);
    CREATE INDEX IF NOT EXISTS idx_raw_responses_query_id ON raw_responses (query_id);
    CREATE INDEX IF NOT EXISTS idx_case_data_query_id ON case_data (query_id);
    CREATE INDEX IF NOT EXISTS idx_case_data_case_number ON case_data (case_number);
  `);

  console.log('Database initialized successfully');
}

// Initialize database on import
initializeDatabase();

export default db;
