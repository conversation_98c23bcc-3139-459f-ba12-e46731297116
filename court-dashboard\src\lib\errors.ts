// Error types and handling utilities

export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  COURT_WEBSITE_ERROR = 'COURT_WEBSITE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  CASE_NOT_FOUND = 'CASE_NOT_FOUND',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}

export interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  code?: string;
  statusCode?: number;
}

export class CourtDataError extends Error {
  public readonly type: ErrorType;
  public readonly details?: any;
  public readonly code?: string;
  public readonly statusCode?: number;

  constructor(type: ErrorType, message: string, details?: any, code?: string, statusCode?: number) {
    super(message);
    this.name = 'CourtDataError';
    this.type = type;
    this.details = details;
    this.code = code;
    this.statusCode = statusCode;
  }
}

// Error factory functions
export const createValidationError = (message: string, details?: any): CourtDataError => {
  return new CourtDataError(ErrorType.VALIDATION_ERROR, message, details, 'VALIDATION_FAILED', 400);
};

export const createAuthenticationError = (message: string = 'Authentication required'): CourtDataError => {
  return new CourtDataError(ErrorType.AUTHENTICATION_ERROR, message, null, 'AUTH_REQUIRED', 401);
};

export const createAuthorizationError = (message: string = 'Access denied'): CourtDataError => {
  return new CourtDataError(ErrorType.AUTHORIZATION_ERROR, message, null, 'ACCESS_DENIED', 403);
};

export const createNetworkError = (message: string = 'Network error occurred'): CourtDataError => {
  return new CourtDataError(ErrorType.NETWORK_ERROR, message, null, 'NETWORK_ERROR', 503);
};

export const createCourtWebsiteError = (message: string, details?: any): CourtDataError => {
  return new CourtDataError(ErrorType.COURT_WEBSITE_ERROR, message, details, 'COURT_UNAVAILABLE', 503);
};

export const createDatabaseError = (message: string, details?: any): CourtDataError => {
  return new CourtDataError(ErrorType.DATABASE_ERROR, message, details, 'DATABASE_ERROR', 500);
};

export const createRateLimitError = (message: string = 'Too many requests'): CourtDataError => {
  return new CourtDataError(ErrorType.RATE_LIMIT_ERROR, message, null, 'RATE_LIMITED', 429);
};

export const createCaseNotFoundError = (caseNumber: string, court: string): CourtDataError => {
  return new CourtDataError(
    ErrorType.CASE_NOT_FOUND, 
    `Case ${caseNumber} not found in ${court}`,
    { caseNumber, court },
    'CASE_NOT_FOUND',
    404
  );
};

export const createInternalError = (message: string = 'Internal server error', details?: any): CourtDataError => {
  return new CourtDataError(ErrorType.INTERNAL_ERROR, message, details, 'INTERNAL_ERROR', 500);
};

// User-friendly error messages
export const getUserFriendlyMessage = (error: CourtDataError | Error): string => {
  if (error instanceof CourtDataError) {
    switch (error.type) {
      case ErrorType.VALIDATION_ERROR:
        return error.message; // Validation messages are already user-friendly
      
      case ErrorType.AUTHENTICATION_ERROR:
        return 'Please sign in to continue';
      
      case ErrorType.AUTHORIZATION_ERROR:
        return 'You do not have permission to perform this action';
      
      case ErrorType.NETWORK_ERROR:
        return 'Network connection error. Please check your internet connection and try again.';
      
      case ErrorType.COURT_WEBSITE_ERROR:
        return 'Court website is temporarily unavailable. Please try again later.';
      
      case ErrorType.DATABASE_ERROR:
        return 'A database error occurred. Please try again later.';
      
      case ErrorType.RATE_LIMIT_ERROR:
        return 'Too many requests. Please wait a moment before trying again.';
      
      case ErrorType.CASE_NOT_FOUND:
        return 'No matching case found. Please verify the case details and try again.';
      
      case ErrorType.INTERNAL_ERROR:
      default:
        return 'An unexpected error occurred. Please try again later.';
    }
  }
  
  return 'An unexpected error occurred. Please try again later.';
};

// Error logging utility
export const logError = (error: Error | CourtDataError, context?: string): void => {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    context,
    message: error.message,
    stack: error.stack,
    ...(error instanceof CourtDataError && {
      type: error.type,
      code: error.code,
      details: error.details
    })
  };
  
  console.error('Application Error:', errorInfo);
  
  // In production, you might want to send this to an error tracking service
  // like Sentry, LogRocket, or similar
};

// Validation helpers
export const validateRequired = (value: any, fieldName: string): void => {
  if (value === null || value === undefined || value === '') {
    throw createValidationError(`${fieldName} is required`);
  }
};

export const validateEmail = (email: string): void => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw createValidationError('Invalid email format');
  }
};

export const validateCaseNumber = (caseNumber: string): void => {
  if (!caseNumber || caseNumber.trim().length === 0) {
    throw createValidationError('Case number is required');
  }
  
  if (caseNumber.length > 50) {
    throw createValidationError('Case number is too long');
  }
  
  // Allow alphanumeric characters, hyphens, and slashes
  const validPattern = /^[A-Za-z0-9\-\/]+$/;
  if (!validPattern.test(caseNumber)) {
    throw createValidationError('Case number contains invalid characters');
  }
};

export const validateFilingYear = (year: number): void => {
  const currentYear = new Date().getFullYear();
  const minYear = currentYear - 50; // Allow cases from last 50 years
  
  if (year < minYear || year > currentYear) {
    throw createValidationError(`Filing year must be between ${minYear} and ${currentYear}`);
  }
};

// API error response helper
export const createErrorResponse = (error: CourtDataError | Error) => {
  if (error instanceof CourtDataError) {
    return {
      success: false,
      error: getUserFriendlyMessage(error),
      code: error.code,
      type: error.type,
      ...(process.env.NODE_ENV === 'development' && { details: error.details })
    };
  }
  
  return {
    success: false,
    error: 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
    type: ErrorType.INTERNAL_ERROR
  };
};

// Retry utility for network operations
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError!;
};

// Circuit breaker pattern for external services
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private threshold: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw createCourtWebsiteError('Service temporarily unavailable');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
