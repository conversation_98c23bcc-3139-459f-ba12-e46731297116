import db from '@/lib/database';
import { CaseData, CreateCaseData, RawResponse, CreateRawResponseData } from '@/types/database';

export class CaseDataModel {
  // Create new case data
  static create(caseData: CreateCaseData): CaseData {
    const stmt = db.prepare(`
      INSERT INTO case_data (
        query_id, case_number, case_type, court_name, 
        petitioner, respondent, filing_date, next_hearing_date, 
        case_status, latest_order_url
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      caseData.query_id,
      caseData.case_number,
      caseData.case_type,
      caseData.court_name,
      caseData.petitioner,
      caseData.respondent,
      caseData.filing_date,
      caseData.next_hearing_date,
      caseData.case_status,
      caseData.latest_order_url
    );
    
    return this.findById(result.lastInsertRowid as number)!;
  }

  // Find case data by ID
  static findById(id: number): CaseData | null {
    const stmt = db.prepare('SELECT * FROM case_data WHERE id = ?');
    return stmt.get(id) as CaseData | null;
  }

  // Find case data by query ID
  static findByQueryId(queryId: number): CaseData | null {
    const stmt = db.prepare('SELECT * FROM case_data WHERE query_id = ?');
    return stmt.get(queryId) as CaseData | null;
  }

  // Find case data by case number and court
  static findByCaseNumber(caseNumber: string, courtName: string): CaseData[] {
    const stmt = db.prepare(`
      SELECT * FROM case_data 
      WHERE case_number = ? AND court_name = ?
      ORDER BY created_at DESC
    `);
    return stmt.all(caseNumber, courtName) as CaseData[];
  }

  // Update case data
  static update(id: number, updates: Partial<Omit<CaseData, 'id' | 'query_id' | 'created_at'>>): CaseData | null {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    
    const stmt = db.prepare(`
      UPDATE case_data 
      SET ${fields}
      WHERE id = ?
    `);
    
    stmt.run(...values, id);
    return this.findById(id);
  }

  // Delete case data
  static delete(id: number): boolean {
    const stmt = db.prepare('DELETE FROM case_data WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Get cases by status
  static findByStatus(status: string, limit = 20, offset = 0): CaseData[] {
    const stmt = db.prepare(`
      SELECT * FROM case_data 
      WHERE case_status = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `);
    return stmt.all(status, limit, offset) as CaseData[];
  }

  // Get cases with upcoming hearings
  static findUpcomingHearings(days = 30): CaseData[] {
    const stmt = db.prepare(`
      SELECT * FROM case_data 
      WHERE next_hearing_date IS NOT NULL 
        AND date(next_hearing_date) BETWEEN date('now') AND date('now', '+${days} days')
      ORDER BY next_hearing_date ASC
    `);
    return stmt.all() as CaseData[];
  }

  // Search cases by petitioner or respondent
  static searchByParty(searchTerm: string, limit = 20): CaseData[] {
    const stmt = db.prepare(`
      SELECT * FROM case_data 
      WHERE petitioner LIKE ? OR respondent LIKE ?
      ORDER BY created_at DESC
      LIMIT ?
    `);
    const searchPattern = `%${searchTerm}%`;
    return stmt.all(searchPattern, searchPattern, limit) as CaseData[];
  }
}

export class RawResponseModel {
  // Create new raw response
  static create(responseData: CreateRawResponseData): RawResponse {
    const stmt = db.prepare(`
      INSERT INTO raw_responses (query_id, raw_html, raw_text, success, error_message)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      responseData.query_id,
      responseData.raw_html,
      responseData.raw_text,
      responseData.success,
      responseData.error_message
    );
    
    return this.findById(result.lastInsertRowid as number)!;
  }

  // Find raw response by ID
  static findById(id: number): RawResponse | null {
    const stmt = db.prepare('SELECT * FROM raw_responses WHERE id = ?');
    return stmt.get(id) as RawResponse | null;
  }

  // Find raw responses by query ID
  static findByQueryId(queryId: number): RawResponse[] {
    const stmt = db.prepare(`
      SELECT * FROM raw_responses 
      WHERE query_id = ?
      ORDER BY response_timestamp DESC
    `);
    return stmt.all(queryId) as RawResponse[];
  }

  // Find successful responses
  static findSuccessful(limit = 50, offset = 0): RawResponse[] {
    const stmt = db.prepare(`
      SELECT * FROM raw_responses 
      WHERE success = TRUE
      ORDER BY response_timestamp DESC
      LIMIT ? OFFSET ?
    `);
    return stmt.all(limit, offset) as RawResponse[];
  }

  // Find failed responses
  static findFailed(limit = 50, offset = 0): RawResponse[] {
    const stmt = db.prepare(`
      SELECT * FROM raw_responses 
      WHERE success = FALSE
      ORDER BY response_timestamp DESC
      LIMIT ? OFFSET ?
    `);
    return stmt.all(limit, offset) as RawResponse[];
  }

  // Delete old raw responses (cleanup)
  static deleteOlderThan(days: number): number {
    const stmt = db.prepare(`
      DELETE FROM raw_responses 
      WHERE response_timestamp < datetime('now', '-${days} days')
    `);
    const result = stmt.run();
    return result.changes;
  }

  // Get response statistics
  static getStats(): {
    total: number;
    successful: number;
    failed: number;
    success_rate: number;
  } {
    const stmt = db.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful,
        SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed
      FROM raw_responses
    `);
    
    const result = stmt.get() as { total: number; successful: number; failed: number };
    const success_rate = result.total > 0 ? (result.successful / result.total) * 100 : 0;
    
    return {
      ...result,
      success_rate: Math.round(success_rate * 100) / 100
    };
  }
}
