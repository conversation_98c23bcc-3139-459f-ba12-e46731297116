import db from '@/lib/database';
import { SearchQuery, CreateSearchQueryData, SearchHistoryItem } from '@/types/database';

export class SearchQueryModel {
  // Create a new search query
  static create(queryData: CreateSearchQueryData): SearchQuery {
    const stmt = db.prepare(`
      INSERT INTO search_queries (user_id, court, case_type, case_number, filing_year, status)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      queryData.user_id,
      queryData.court,
      queryData.case_type,
      queryData.case_number,
      queryData.filing_year,
      queryData.status || 'pending'
    );
    
    return this.findById(result.lastInsertRowid as number)!;
  }

  // Find search query by ID
  static findById(id: number): SearchQuery | null {
    const stmt = db.prepare('SELECT * FROM search_queries WHERE id = ?');
    return stmt.get(id) as SearchQuery | null;
  }

  // Find search queries by user ID
  static findByUserId(userId: number, limit = 20, offset = 0): SearchQuery[] {
    const stmt = db.prepare(`
      SELECT * FROM search_queries 
      WHERE user_id = ? 
      ORDER BY search_timestamp DESC 
      LIMIT ? OFFSET ?
    `);
    return stmt.all(userId, limit, offset) as SearchQuery[];
  }

  // Get search history with case data for a user
  static getSearchHistoryWithCaseData(userId: number, limit = 20, offset = 0): SearchHistoryItem[] {
    const stmt = db.prepare(`
      SELECT 
        sq.*,
        cd.id as case_data_id,
        cd.petitioner,
        cd.respondent,
        cd.filing_date,
        cd.next_hearing_date,
        cd.case_status,
        cd.latest_order_url
      FROM search_queries sq
      LEFT JOIN case_data cd ON sq.id = cd.query_id
      WHERE sq.user_id = ?
      ORDER BY sq.search_timestamp DESC
      LIMIT ? OFFSET ?
    `);
    
    const results = stmt.all(userId, limit, offset) as any[];
    
    return results.map(row => ({
      id: row.id,
      user_id: row.user_id,
      court: row.court,
      case_type: row.case_type,
      case_number: row.case_number,
      filing_year: row.filing_year,
      search_timestamp: row.search_timestamp,
      status: row.status,
      case_data: row.case_data_id ? {
        id: row.case_data_id,
        query_id: row.id,
        case_number: row.case_number,
        case_type: row.case_type,
        court_name: row.court,
        petitioner: row.petitioner,
        respondent: row.respondent,
        filing_date: row.filing_date,
        next_hearing_date: row.next_hearing_date,
        case_status: row.case_status,
        latest_order_url: row.latest_order_url,
        created_at: row.search_timestamp
      } : undefined
    }));
  }

  // Update search query status
  static updateStatus(id: number, status: 'pending' | 'completed' | 'failed'): SearchQuery | null {
    const stmt = db.prepare(`
      UPDATE search_queries 
      SET status = ? 
      WHERE id = ?
    `);
    
    stmt.run(status, id);
    return this.findById(id);
  }

  // Delete search query
  static delete(id: number): boolean {
    const stmt = db.prepare('DELETE FROM search_queries WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Find recent searches by user
  static findRecentByUserId(userId: number, days = 7): SearchQuery[] {
    const stmt = db.prepare(`
      SELECT * FROM search_queries 
      WHERE user_id = ? 
        AND search_timestamp >= datetime('now', '-${days} days')
      ORDER BY search_timestamp DESC
    `);
    return stmt.all(userId) as SearchQuery[];
  }

  // Find duplicate searches
  static findDuplicate(userId: number, court: string, caseType: string, caseNumber: string, filingYear: number): SearchQuery | null {
    const stmt = db.prepare(`
      SELECT * FROM search_queries 
      WHERE user_id = ? 
        AND court = ? 
        AND case_type = ? 
        AND case_number = ? 
        AND filing_year = ?
      ORDER BY search_timestamp DESC 
      LIMIT 1
    `);
    return stmt.get(userId, court, caseType, caseNumber, filingYear) as SearchQuery | null;
  }

  // Get search statistics for a user
  static getUserSearchStats(userId: number): {
    total: number;
    successful: number;
    failed: number;
    pending: number;
  } {
    const stmt = db.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
      FROM search_queries 
      WHERE user_id = ?
    `);
    
    return stmt.get(userId) as {
      total: number;
      successful: number;
      failed: number;
      pending: number;
    };
  }

  // Get popular courts
  static getPopularCourts(limit = 10): { court: string; count: number }[] {
    const stmt = db.prepare(`
      SELECT court, COUNT(*) as count 
      FROM search_queries 
      GROUP BY court 
      ORDER BY count DESC 
      LIMIT ?
    `);
    return stmt.all(limit) as { court: string; count: number }[];
  }

  // Get popular case types
  static getPopularCaseTypes(limit = 10): { case_type: string; count: number }[] {
    const stmt = db.prepare(`
      SELECT case_type, COUNT(*) as count 
      FROM search_queries 
      GROUP BY case_type 
      ORDER BY count DESC 
      LIMIT ?
    `);
    return stmt.all(limit) as { case_type: string; count: number }[];
  }
}
