import db from '@/lib/database';
import { User, CreateUserData } from '@/types/database';
import bcrypt from 'bcryptjs';

export class UserModel {
  // Create a new user
  static create(userData: CreateUserData): User {
    const stmt = db.prepare(`
      INSERT INTO users (name, email, password_hash)
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(userData.name, userData.email, userData.password_hash);
    
    return this.findById(result.lastInsertRowid as number)!;
  }

  // Find user by ID
  static findById(id: number): User | null {
    const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
    return stmt.get(id) as User | null;
  }

  // Find user by email
  static findByEmail(email: string): User | null {
    const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
    return stmt.get(email) as User | null;
  }

  // Update user
  static update(id: number, updates: Partial<Omit<User, 'id' | 'created_at'>>): User | null {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    
    const stmt = db.prepare(`
      UPDATE users 
      SET ${fields}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `);
    
    stmt.run(...values, id);
    return this.findById(id);
  }

  // Delete user
  static delete(id: number): boolean {
    const stmt = db.prepare('DELETE FROM users WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Verify password
  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  // Hash password
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  // Create user with hashed password
  static async createWithHashedPassword(name: string, email: string, password: string): Promise<User> {
    const hashedPassword = await this.hashPassword(password);
    return this.create({
      name,
      email,
      password_hash: hashedPassword
    });
  }

  // Authenticate user
  static async authenticate(email: string, password: string): Promise<User | null> {
    const user = this.findByEmail(email);
    if (!user) return null;

    const isValid = await this.verifyPassword(password, user.password_hash);
    return isValid ? user : null;
  }

  // Check if email exists
  static emailExists(email: string): boolean {
    const stmt = db.prepare('SELECT COUNT(*) as count FROM users WHERE email = ?');
    const result = stmt.get(email) as { count: number };
    return result.count > 0;
  }

  // Get all users (admin function)
  static findAll(limit = 50, offset = 0): User[] {
    const stmt = db.prepare(`
      SELECT * FROM users 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `);
    return stmt.all(limit, offset) as User[];
  }

  // Get user count
  static count(): number {
    const stmt = db.prepare('SELECT COUNT(*) as count FROM users');
    const result = stmt.get() as { count: number };
    return result.count;
  }
}
