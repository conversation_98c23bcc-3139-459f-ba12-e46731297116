// Database types and interfaces

export interface User {
  id: number;
  name: string;
  email: string;
  password_hash: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUserData {
  name: string;
  email: string;
  password_hash: string;
}

export interface SearchQuery {
  id: number;
  user_id: number;
  court: string;
  case_type: string;
  case_number: string;
  filing_year: number;
  search_timestamp: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface CreateSearchQueryData {
  user_id: number;
  court: string;
  case_type: string;
  case_number: string;
  filing_year: number;
  status?: 'pending' | 'completed' | 'failed';
}

export interface RawResponse {
  id: number;
  query_id: number;
  raw_html?: string;
  raw_text?: string;
  response_timestamp: string;
  success: boolean;
  error_message?: string;
}

export interface CreateRawResponseData {
  query_id: number;
  raw_html?: string;
  raw_text?: string;
  success: boolean;
  error_message?: string;
}

export interface CaseData {
  id: number;
  query_id: number;
  case_number: string;
  case_type: string;
  court_name: string;
  petitioner?: string;
  respondent?: string;
  filing_date?: string;
  next_hearing_date?: string;
  case_status?: string;
  latest_order_url?: string;
  created_at: string;
}

export interface CreateCaseData {
  query_id: number;
  case_number: string;
  case_type: string;
  court_name: string;
  petitioner?: string;
  respondent?: string;
  filing_date?: string;
  next_hearing_date?: string;
  case_status?: string;
  latest_order_url?: string;
}

// Court and case type options
export const COURTS = [
  'Delhi High Court',
  'Faridabad District Court',
  'Mumbai High Court',
  'Kolkata High Court',
  'Chennai High Court',
  'Bangalore High Court'
] as const;

export const CASE_TYPES = [
  'CIVIL',
  'CRL',
  'WP',
  'CWP',
  'CRP',
  'FAO',
  'LPA',
  'MAC',
  'BAIL',
  'MISC'
] as const;

export type Court = typeof COURTS[number];
export type CaseType = typeof CASE_TYPES[number];

// Search form data
export interface SearchFormData {
  court: Court;
  case_type: CaseType;
  case_number: string;
  filing_year: number;
}

// API response types
export interface CaseSearchResult {
  success: boolean;
  data?: CaseData;
  error?: string;
  message?: string;
}

export interface SearchHistoryItem extends SearchQuery {
  case_data?: CaseData;
}

export interface UserSearchHistory {
  queries: SearchHistoryItem[];
  total: number;
  page: number;
  limit: number;
}
